import { describe, it, expect } from 'vitest';
import { createOptimizedWithdrawalLimitsPayload } from '../useWithdrawalLimitChanges';
import { WithdrawalLimitType } from '+types';

describe('useWithdrawalLimitChanges', () => {
  describe('createOptimizedWithdrawalLimitsPayload', () => {
    const originalData: WithdrawalLimitType = {
      web: {
        daily: { settlement_account: 100, non_settlement_account: 50 },
        per_transaction: {
          settlement_account: { min: 10, max: 1000 },
          non_settlement_account: { min: 5, max: 500 }
        }
      },
      api: {
        per_transaction: { min: 20, max: 2000 }
      },
      global: {
        daily: 5000
      }
    };

    it('should return empty object when no data is provided', () => {
      const result = createOptimizedWithdrawalLimitsPayload(undefined, undefined);
      expect(result).toEqual({});
    });

    it('should return empty object when data is identical', () => {
      const result = createOptimizedWithdrawalLimitsPayload(originalData, originalData);
      expect(result).toEqual({});
    });

    it('should return only changed web section', () => {
      const currentData: WithdrawalLimitType = {
        ...originalData,
        web: {
          daily: { settlement_account: 200, non_settlement_account: 50 }, // Changed
          per_transaction: {
            settlement_account: { min: 10, max: 1000 },
            non_settlement_account: { min: 5, max: 500 }
          }
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(originalData, currentData);
      expect(result).toEqual({
        web: currentData.web
      });
    });

    it('should return only changed api section', () => {
      const currentData: WithdrawalLimitType = {
        ...originalData,
        api: {
          per_transaction: { min: 30, max: 2000 } // Changed min value
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(originalData, currentData);
      expect(result).toEqual({
        api: currentData.api
      });
    });

    it('should return multiple changed sections', () => {
      const currentData: WithdrawalLimitType = {
        ...originalData,
        web: {
          daily: { settlement_account: 200, non_settlement_account: 50 }, // Changed
          per_transaction: {
            settlement_account: { min: 10, max: 1000 },
            non_settlement_account: { min: 5, max: 500 }
          }
        },
        global: {
          daily: 6000 // Changed
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(originalData, currentData);
      expect(result).toEqual({
        web: currentData.web,
        global: currentData.global
      });
    });

    it('should respect exclude conditions', () => {
      const currentData: WithdrawalLimitType = {
        ...originalData,
        web: {
          daily: { settlement_account: 200, non_settlement_account: 50 }, // Changed
          per_transaction: {
            settlement_account: { min: 10, max: 1000 },
            non_settlement_account: { min: 5, max: 500 }
          }
        },
        api: {
          per_transaction: { min: 30, max: 2000 } // Changed
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(
        originalData, 
        currentData, 
        { web: true } // Exclude web
      );
      
      expect(result).toEqual({
        api: currentData.api
      });
    });

    it('should handle new sections that did not exist in original', () => {
      const originalDataWithoutGlobal: WithdrawalLimitType = {
        web: originalData.web!,
        api: originalData.api!
      };

      const currentData: WithdrawalLimitType = {
        ...originalDataWithoutGlobal,
        global: {
          daily: 5000 // New section
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(originalDataWithoutGlobal, currentData);
      expect(result).toEqual({
        global: currentData.global
      });
    });

    it('should handle deep nested changes correctly', () => {
      const currentData: WithdrawalLimitType = {
        ...originalData,
        web: {
          ...originalData.web!,
          per_transaction: {
            settlement_account: { min: 15, max: 1000 }, // Changed min
            non_settlement_account: { min: 5, max: 500 }
          }
        }
      };

      const result = createOptimizedWithdrawalLimitsPayload(originalData, currentData);
      expect(result).toEqual({
        web: currentData.web
      });
    });
  });
});
