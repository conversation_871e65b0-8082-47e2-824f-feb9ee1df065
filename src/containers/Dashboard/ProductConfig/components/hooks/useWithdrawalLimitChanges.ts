import { useCallback, useRef, useState } from 'react';
import { WithdrawalLimitType, WithdrawalLimitTypeKey } from '+types';
import { isEqual } from 'lodash';

type WithdrawalLimitSection = 'web' | 'api' | 'global';

interface UseWithdrawalLimitChangesOptions {
  initialData?: WithdrawalLimitType;
  excludeConditions?: {
    web?: boolean;
    api?: boolean;
    global?: boolean;
  };
}

interface WithdrawalLimitChanges {
  hasChanges: boolean;
  changedSections: WithdrawalLimitSection[];
  getOptimizedPayload: () => Partial<WithdrawalLimitType>;
  trackChange: (section: WithdrawalLimitTypeKey, newData: any) => void;
  resetChanges: () => void;
  isChanged: (section: WithdrawalLimitSection) => boolean;
}

/**
 * Hook to track withdrawal limit changes and generate optimized payloads
 * Only includes sections that were actually modified by the user
 */
export const useWithdrawalLimitChanges = (
  options: UseWithdrawalLimitChangesOptions = {}
): WithdrawalLimitChanges => {
  const { initialData, excludeConditions = {} } = options;
  
  // Store the original data for comparison
  const originalDataRef = useRef<WithdrawalLimitType | undefined>(initialData);
  const [changedSections, setChangedSections] = useState<Set<WithdrawalLimitSection>>(new Set());
  const [currentData, setCurrentData] = useState<WithdrawalLimitType | undefined>(initialData);

  // Update original data when initialData changes
  if (initialData && !isEqual(originalDataRef.current, initialData)) {
    originalDataRef.current = initialData;
    setCurrentData(initialData);
    setChangedSections(new Set());
  }

  const trackChange = useCallback((section: WithdrawalLimitTypeKey, newData: any) => {
    if (!originalDataRef.current) return;

    const originalSection = originalDataRef.current[section];
    const hasChanged = !isEqual(originalSection, newData);

    setChangedSections(prev => {
      const updated = new Set(prev);
      if (hasChanged) {
        updated.add(section as WithdrawalLimitSection);
      } else {
        updated.delete(section as WithdrawalLimitSection);
      }
      return updated;
    });

    setCurrentData(prev => prev ? { ...prev, [section]: newData } : undefined);
  }, []);

  const isChanged = useCallback((section: WithdrawalLimitSection): boolean => {
    return changedSections.has(section);
  }, [changedSections]);

  const getOptimizedPayload = useCallback((): Partial<WithdrawalLimitType> => {
    if (!currentData) return {};

    const payload: Partial<WithdrawalLimitType> = {};

    // Only include sections that were changed and not excluded
    changedSections.forEach(section => {
      if (!excludeConditions[section]) {
        payload[section] = currentData[section];
      }
    });

    return payload;
  }, [currentData, changedSections, excludeConditions]);

  const resetChanges = useCallback(() => {
    setChangedSections(new Set());
    setCurrentData(originalDataRef.current);
  }, []);

  return {
    hasChanges: changedSections.size > 0,
    changedSections: Array.from(changedSections),
    getOptimizedPayload,
    trackChange,
    resetChanges,
    isChanged
  };
};

/**
 * Utility function to create optimized withdrawal limits payload
 * @param originalData - The original withdrawal limit data
 * @param currentData - The current withdrawal limit data
 * @param excludeConditions - Conditions to exclude certain sections
 * @returns Optimized payload with only changed sections
 */
export const createOptimizedWithdrawalLimitsPayload = (
  originalData: WithdrawalLimitType | undefined,
  currentData: WithdrawalLimitType | undefined,
  excludeConditions: { web?: boolean; api?: boolean; global?: boolean } = {}
): Partial<WithdrawalLimitType> => {
  if (!originalData || !currentData) return {};

  const payload: Partial<WithdrawalLimitType> = {};
  const sections: WithdrawalLimitSection[] = ['web', 'api', 'global'];

  sections.forEach(section => {
    // Skip if excluded by condition
    if (excludeConditions[section]) return;

    // Skip if section doesn't exist in current data
    if (!currentData[section]) return;

    // Include if changed or if it's a new section
    const hasChanged = !isEqual(originalData[section], currentData[section]);
    const isNewSection = !originalData[section] && currentData[section];

    if (hasChanged || isNewSection) {
      payload[section] = currentData[section];
    }
  });

  return payload;
};
