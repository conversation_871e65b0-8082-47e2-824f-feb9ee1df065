import { updateProductConfigData, WithdrawalLimitType } from '+types';

type BankAccountWithChannels = {
  bank_account: {
    channels: string[];
  };
};

export const limitsLabel = {
  min: 'Minimum limit per transaction',
  max: 'Maximum limit per transaction',
  settlement_account: 'Daily limit for settlement account',
  non_settlement_account: 'Daily limit for non-settlement account'
};

export const defaultWithdrawalLimits = (content: WithdrawalLimitType, merchantConfigType?: updateProductConfigData['type']) => {
  const web = content?.web;
  const api = content?.api;
  const global = content?.global;

  return merchantConfigType === 'custom_merchants'
    ? {
        web: {
          daily: {
            settlement_account: 0,
            non_settlement_account: 0
          },
          settlement: {
            min: 0,
            max: 0
          },
          'non-settlement': {
            min: 0,
            max: 0
          }
        },
        api: {
          min: 0,
          max: 0
        },
        global: {
          daily: 0
        }
      }
    : {
        web: {
          daily: {
            settlement_account: web?.daily?.settlement_account ?? 0,
            non_settlement_account: web?.daily?.non_settlement_account ?? 0
          },
          settlement: {
            min: web?.per_transaction?.settlement_account?.min ?? 0,
            max: web?.per_transaction?.settlement_account?.max ?? 0
          },
          'non-settlement': {
            min: web?.per_transaction?.non_settlement_account?.min ?? 0,
            max: web?.per_transaction?.non_settlement_account?.max ?? 0
          }
        },
        api: {
          min: api?.per_transaction?.min ?? 0,
          max: api?.per_transaction?.max ?? 0
        },
        global: {
          daily: global?.daily ?? 0
        }
      };
};

export const withdrawalTypeTabs = [
  { label: 'Web Limit', value: 'web' },
  { label: 'API Limit', value: 'api' },
  { label: 'Global Limit', value: 'global' }
];

export const getAvailableWithdrawalTabs = (content?: unknown) => {
  const baseOptions = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

  if (!content) return baseOptions;

  const typedContent = content as Record<string, unknown>;

  if (Array.isArray(typedContent.channels)) {
    const channels = typedContent.channels as string[];
    const availableOptions = channels
      .map(channel => withdrawalTypeTabs.find(tab => tab.value === channel))
      .filter((tab): tab is (typeof withdrawalTypeTabs)[0] => tab !== undefined && tab.value !== 'global');

    return availableOptions.length > 0 ? availableOptions : baseOptions;
  }

  const hasBankAccountChannels = (obj: unknown): obj is BankAccountWithChannels => {
    return (
      obj !== null &&
      typeof obj === 'object' &&
      'bank_account' in obj &&
      obj.bank_account !== null &&
      typeof obj.bank_account === 'object' &&
      'channels' in obj.bank_account &&
      Array.isArray((obj.bank_account as any).channels)
    );
  };

  if (hasBankAccountChannels(content)) {
    const channels = content.bank_account.channels;

    const availableOptions = channels
      .map(channel => withdrawalTypeTabs.find(tab => tab.value === channel))
      .filter((tab): tab is (typeof withdrawalTypeTabs)[0] => tab !== undefined && tab.value !== 'global');

    return availableOptions.length > 0 ? availableOptions : baseOptions;
  }


  const isWithdrawalLimitType = (obj: unknown): obj is WithdrawalLimitType => {
    return obj !== null && typeof obj === 'object' && ('web' in obj || 'api' in obj);
  };

  if (!isWithdrawalLimitType(content)) return baseOptions;

  const data = content as WithdrawalLimitType;
  const availableOptions = baseOptions.filter(tab => {
    if (tab.value === 'web') {
      return !!(data.web && (data.web.daily || data.web.per_transaction));
    }
    if (tab.value === 'api') {
      return !!(data.api && data.api.per_transaction);
    }
    return true;
  });

  return availableOptions.length > 0 ? availableOptions : baseOptions;
};

export const getDefaultWithdrawalTab = (content?: unknown) => {
  const options = getAvailableWithdrawalTabs(content);
  return (options[0]?.value ?? 'web') as 'web' | 'api';
};
export const getWithdrawalTabsForContext = (context: 'global' | 'product', content?: unknown) => {
  if (context === 'global') {
    return withdrawalTypeTabs;
  } else {
    return getAvailableWithdrawalTabs(content);
  }
};
