import { describe, expect, it } from 'vitest';

import { getAvailableWithdrawalTabs, getDefaultWithdrawalTab, withdrawalTypeTabs } from '../withdrawalLimitHelpers';

describe('withdrawalLimitHelpers', () => {
  describe('getAvailableWithdrawalTabs', () => {
    it('should return base options (web and api) when no content is provided', () => {
      const result = getAvailableWithdrawalTabs();
      const expected = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

      expect(result).toEqual(expected);
      expect(result).toHaveLength(2);
      expect(result.map(tab => tab.value)).toEqual(['web', 'api']);
    });

    it('should return base options when content is null or undefined', () => {
      expect(getAvailableWithdrawalTabs(null)).toEqual(withdrawalTypeTabs.filter(tab => tab.value !== 'global'));
      expect(getAvailableWithdrawalTabs(undefined)).toEqual(withdrawalTypeTabs.filter(tab => tab.value !== 'global'));
    });

    describe('with bank_account channels', () => {
      it('should return tabs based on bank_account channels when both web and api are present', () => {
        const content = {
          bank_account: {
            channels: ['web', 'api']
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['web', 'api']);
      });

      it('should return only web tab when only web channel is present', () => {
        const content = {
          bank_account: {
            channels: ['web']
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(1);
        expect(result[0].value).toBe('web');
        expect(result[0].label).toBe('Web Limit');
      });

      it('should return only api tab when only api channel is present', () => {
        const content = {
          bank_account: {
            channels: ['api']
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(1);
        expect(result[0].value).toBe('api');
        expect(result[0].label).toBe('API Limit');
      });

      it('should return base options when channels array is empty', () => {
        const content = {
          bank_account: {
            channels: []
          }
        };

        const result = getAvailableWithdrawalTabs(content);
        const expected = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

        expect(result).toEqual(expected);
      });

      it('should ignore global channel in bank_account channels (should not be included in base options)', () => {
        const content = {
          bank_account: {
            channels: ['web', 'api', 'global']
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['web', 'api']);
      });

      it('should handle invalid channels gracefully', () => {
        const content = {
          bank_account: {
            channels: ['web', 'invalid_channel', 'api']
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['web', 'api']);
      });
    });

    describe('fallback to original logic', () => {
      it('should use original logic when content has web and api properties but no bank_account', () => {
        const content = {
          web: {
            daily: { settlement_account: 100, non_settlement_account: 200 },
            per_transaction: {
              settlement_account: { min: 10, max: 1000 },
              non_settlement_account: { min: 20, max: 2000 }
            }
          },
          api: {
            per_transaction: { min: 5, max: 500 }
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['web', 'api']);
      });

      it('should filter out web tab when web data is incomplete in original logic', () => {
        const content = {
          web: {},
          api: {
            per_transaction: { min: 5, max: 500 }
          }
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(1);
        expect(result[0].value).toBe('api');
      });

      it('should filter out api tab when api data is incomplete in original logic', () => {
        const content = {
          web: {
            daily: { settlement_account: 100, non_settlement_account: 200 }
          },
          api: {}
        };

        const result = getAvailableWithdrawalTabs(content);

        expect(result).toHaveLength(1);
        expect(result[0].value).toBe('web');
      });
    });

    describe('edge cases', () => {
      it('should handle malformed bank_account structure', () => {
        const content = {
          bank_account: {}
        };

        const result = getAvailableWithdrawalTabs(content);
        const expected = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

        expect(result).toEqual(expected);
      });

      it('should handle bank_account with non-array channels', () => {
        const content = {
          bank_account: {
            channels: 'web'
          }
        };

        const result = getAvailableWithdrawalTabs(content);
        const expected = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

        expect(result).toEqual(expected);
      });

      it('should handle bank_account being null', () => {
        const content = {
          bank_account: null
        };

        const result = getAvailableWithdrawalTabs(content);
        const expected = withdrawalTypeTabs.filter(tab => tab.value !== 'global');

        expect(result).toEqual(expected);
      });
    });

    describe('integration with useLimit hook', () => {
      it('should work with the actual configuration data structure from useLimit', () => {
        const configData = {
          bank_account: {
            enabled: true,
            channels: ['api', 'web'],
            transaction_limit: { min: 100, max: 100000 },
            limits: {
              web: {
                daily: { settlement_account: 5000, non_settlement_account: 4000 },
                per_transaction: {
                  settlement_account: { min: 100, max: 3000 },
                  non_settlement_account: { min: 200, max: 1000 }
                }
              },
              api: { per_transaction: { min: 50, max: 500 } }
            }
          }
        };

        const result = getAvailableWithdrawalTabs(configData);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['api', 'web']);
      });

      it('should prioritize bank_account channels over existing limits data', () => {
        const configData = {
          bank_account: {
            enabled: true,
            channels: ['web'],
            transaction_limit: { min: 100, max: 100000 },
            limits: {
              web: {
                daily: { settlement_account: 5000, non_settlement_account: 4000 },
                per_transaction: {
                  settlement_account: { min: 100, max: 3000 },
                  non_settlement_account: { min: 200, max: 1000 }
                }
              },
              api: { per_transaction: { min: 50, max: 500 } }
            }
          },

          web: {
            daily: { settlement_account: 5000, non_settlement_account: 4000 },
            per_transaction: {
              settlement_account: { min: 100, max: 3000 },
              non_settlement_account: { min: 200, max: 1000 }
            }
          },
          api: { per_transaction: { min: 50, max: 500 } }
        };

        const result = getAvailableWithdrawalTabs(configData);

        expect(result).toHaveLength(1);
        expect(result[0].value).toBe('web');
        expect(result[0].label).toBe('Web Limit');
      });

      it('should work with the updated useLimit hook data structure', () => {
        const contentFromUseLimit = {
          limit: {
            max: 100000,
            min: 100
          },
          channels: ['api', 'web'],

          web: {
            daily: { settlement_account: 5000, non_settlement_account: 4000 },
            per_transaction: {
              settlement_account: { min: 100, max: 3000 },
              non_settlement_account: { min: 200, max: 1000 }
            }
          },
          api: { per_transaction: { min: 50, max: 500 } }
        };

        const result = getAvailableWithdrawalTabs(contentFromUseLimit);

        expect(result).toHaveLength(2);
        expect(result.map(tab => tab.value)).toEqual(['api', 'web']);
        expect(result[0].label).toBe('API Limit');
        expect(result[1].label).toBe('Web Limit');
      });
    });
  });

  describe('getDefaultWithdrawalTab', () => {
    it('should return first available tab from bank_account channels', () => {
      const content = {
        bank_account: {
          channels: ['api', 'web']
        }
      };

      const result = getDefaultWithdrawalTab(content);

      expect(result).toBe('api');
    });

    it('should return web as default when no content provided', () => {
      const result = getDefaultWithdrawalTab();

      expect(result).toBe('web');
    });

    it('should return web as default when channels array is empty', () => {
      const content = {
        bank_account: {
          channels: []
        }
      };

      const result = getDefaultWithdrawalTab(content);

      expect(result).toBe('web');
    });
  });
});
