/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { useMutation } from 'react-query';

import Icon from '+containers/Dashboard/Shared/Icons';
import TabSwitch from '+containers/Dashboard/Shared/TabSwitch';
import Typography from '+containers/Dashboard/Shared/Typography';
import { useSetUserAccess } from '+hooks';
import APIRequest from '+services/api-services';
import Modal from '+shared/Modal';
import {
  EditDetailsCardPropsType,
  IError,
  IModalProps,
  ModalType,
  TransactionLimitType,
  updateProductConfigData,
  updateProductConfigDataTypeWithoutEnabled,
  UpdateVbaCountDataType,
  VbaCountsDataType,
  VbaCountType,
  WithdrawalLimitType
} from '+types';
import {
  backwardAmountInput,
  capitalize,
  capitalizeAllCharacters,
  capitalizeRemovedash,
  CATEGORIES,
  cleanInput,
  CONFIG_TITLES,
  formatAmount,
  formatWithCommas,
  isAllowed,
  logError,
  PAYMENT_METHODS,
  productMapping
} from '+utils';

import { conditionToDisplayBanner, getHeaderLabel } from '../ProductConfigHelper';
import useLimit from './hooks/useLimit';
import { createOptimizedWithdrawalLimitsPayload } from './hooks/useWithdrawalLimitChanges';
import IncreasedVbaCountForm, { TVbaIncreaseFormFields, VBA_LIMIT_MAX_COUNT } from './IncreaseVbaCountForm';
import ProductConfigBanner from './ProductConfigBanner';
import VbaLimitDetails from './VbaLimitDetailsContent';
import WithdrawalLimitContent from './WithdrawalLimit/WithdrawalLimitContent';
import WithdrawalLimitDetails from './WithdrawalLimit/WithdrawalLimitDetails';

import './index.scss';

const apiRequest = new APIRequest();
const payinChannels = {
  api: 'API',
  web: 'Dashboard',
  modal: 'Checkout'
} as const;

const payoutChannels = {
  api: 'API',
  web: 'Dashboard'
} as const;

type channelsType = keyof typeof payinChannels | keyof typeof payoutChannels;

const MIN_WORD_COUNT = 5;

const limitsLabel = {
  min: 'Minimum limit per transaction',
  max: 'Maximum limit per transaction'
};

const configurationMode = {
  default: 'default',
  custom_merchants: 'custom',
  all_merchants: 'default and custom'
} as const;

const nonMobileMoneyCurrency = ['NGN'];

type limitsLabelType = keyof typeof limitsLabel;

const EditDetailsCard = ({
  title,
  content,
  type,
  category,
  merchantId,
  paymentMethod,
  currency,
  disableEdit
}: EditDetailsCardPropsType) => {
  const userAccess = useSetUserAccess();
  const {
    getActiveTabName,
    activeWithdrawalLimitType,
    handleWithdrawalLimitChange,
    anyValidationError,
    withdrawalLimitData,
    handleTabChange,
    radioLabel,
    modal,
    setModal,
    selected,
    setSelected,
    feedbackInit,
    closeFeedback,
    consent,
    setConsent,
    queryClient,
    getTabOptions,
    resetWithdrawalLimitData
  } = useLimit('product');

  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);

  const [limits, setLimits] = useState({
    min: 0,
    max: 0
  });
  const [vbaCountsData, setVbaCountsData] = useState<VbaCountsDataType>({
    num: 0,
    reason: ''
  });

  // Track original withdrawal limit data for change detection
  const [originalWithdrawalLimitData, setOriginalWithdrawalLimitData] = useState<WithdrawalLimitType | undefined>();

  // Store original data when withdrawalLimitData first loads
  useEffect(() => {
    if (withdrawalLimitData && !originalWithdrawalLimitData) {
      console.log('Setting original withdrawal limit data:', withdrawalLimitData);
      setOriginalWithdrawalLimitData(JSON.parse(JSON.stringify(withdrawalLimitData)));
    }
  }, [withdrawalLimitData, originalWithdrawalLimitData]);

  const selectedMode = configurationMode[selected as keyof typeof configurationMode];
  useEffect(() => {
    if (modal !== 'transaction_limit') return;
    const max = +limits.max;
    const min = +limits.min;
    if (max <= min) {
      feedbackInit({
        message: 'Minimum transaction limit must be less than maximum transaction limit',
        componentLevel: true,
        type: 'warning',
        isClosable: false
      });
    } else if (max > min) {
      closeFeedback();
    }
  }, [limits.max, limits.min]);

  const isMerchant = merchantId ? `merchant’s` : '';

  const removeWebLimitForNonMobileMoney = nonMobileMoneyCurrency.includes(currency) && paymentMethod === PAYMENT_METHODS.MOBILE_MONEY;

  const description = {
    channels: `Here you can find the checkout and API products for this ${isMerchant} ${capitalizeRemovedash(
      paymentMethod ?? type
    )} configuration. You can modify these payment channels configuration here.`,
    transaction_limit: `Here you can find the limits for this ${isMerchant} ${capitalizeRemovedash(
      paymentMethod ?? type
    )} configuration. You can modify these limits configuration here.`,
    can_send_to_any_merchant: `Here you can find merchant's config to send payment to other merchants through this product.`,
    vba_count: 'Here you can find the count of all VBAs and how they are been used. You can increase the VBA count here.',
    limits: `Here you can find the withdrawal limits for this ${isMerchant} ${capitalizeRemovedash(
      paymentMethod ?? type
    )} configuration. You can modify these limits configuration here.`
  };

  useEffect(() => {
    if (Array.isArray(content) && content.length > 0 && modal === 'channels') {
      setSelectedChannels(prev => [...prev, ...content]);
    }
  }, [content, modal]);

  useEffect(() => {
    if (!Array.isArray(content) && modal === 'transaction_limit') {
      if ('max' in content && 'min' in content) {
        setLimits({
          min: content?.min || 0,
          max: content?.max || 0
        });
      }
    }
  }, [modal]);

  const updateProductConfiguration = useMutation((value: updateProductConfigData) => apiRequest.updateProductConfiguration(value), {
    onSuccess: () => {
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, merchantId, currency, category]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency, `${category?.toLowerCase()}`]);
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG`, currency, `${category?.toLowerCase()}`]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency, `${category.toLowerCase()}`, paymentMethod]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency]);
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG_SETTING`, currency]);
      queryClient.invalidateQueries(`${currency}_MERCHANTS`);
      setConsent(false);
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: `${(error as IError)?.response?.data?.message || "There has been an error updating merchant's configuration"}`,
        type: 'danger',
        componentLevel: true
      });
    }
  });
  const updateVbaCount = useMutation((value: UpdateVbaCountDataType) => apiRequest.updateVbaCountService(value, merchantId || ''), {
    onSuccess: () => {
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, merchantId, currency, category]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency, `${category?.toLowerCase()}`]);
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG`, currency, `${category?.toLowerCase()}`]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency, `${category.toLowerCase()}`, paymentMethod]);
      queryClient.invalidateQueries([`${currency}_PRODUCT_CONFIG`, currency]);
      queryClient.invalidateQueries([`${currency}_ALL_PRODUCT_CONFIG_SETTING`, currency]);
      queryClient.invalidateQueries(`${currency}_MERCHANTS`);
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: `${(error as IError)?.response?.data?.message || 'There has been an error increasing VBA count'}`,
        type: 'danger',
        componentLevel: true
      });
    }
  });

  const allAccessContent = () => {
    return (
      <div className="currency-modal__content">
        <div className="radio_container">
          {['default', 'all_merchants', 'custom_merchants'].map(item => {
            return (
              <label key={item}>
                <input checked={item === selected} type="radio" onChange={() => setSelected(item as keyof typeof radioLabel)} />
                {`Edit ${radioLabel[item as keyof typeof radioLabel]}`}
              </label>
            );
          })}
        </div>
      </div>
    );
  };
  const removeChannel = (current: channelsType) => {
    if (category === CATEGORIES.PAY_INS) {
      if (
        [PAYMENT_METHODS.CARD, PAYMENT_METHODS.MOBILE_MONEY, PAYMENT_METHODS.BANK_TRANSFER].includes(
          paymentMethod as typeof PAYMENT_METHODS.CARD | typeof PAYMENT_METHODS.MOBILE_MONEY | typeof PAYMENT_METHODS.BANK_TRANSFER
        )
      ) {
        if (!['api', 'modal'].includes(current)) return true;
      } else if ([PAYMENT_METHODS.PAY_WITH_BANK].includes(paymentMethod as typeof PAYMENT_METHODS.PAY_WITH_BANK)) {
        if (['ZAR'].includes(currency)) {
          if (!['modal', 'api'].includes(current)) return true;
        } else if (!['modal'].includes(current)) return true;
      } else if ([PAYMENT_METHODS.DISBURSEMENT_WALLET].includes(paymentMethod as typeof PAYMENT_METHODS.DISBURSEMENT_WALLET)) {
        if (!['web'].includes(current)) return true;
      } else if ([PAYMENT_METHODS.VIRTUAL_BANK_ACCOUNT].includes(paymentMethod as typeof PAYMENT_METHODS.VIRTUAL_BANK_ACCOUNT)) {
        if (!['api'].includes(current)) return true;
      }
    } else if (
      [
        PAYMENT_METHODS.BANK_TRANSFER,
        PAYMENT_METHODS.BANK_ACCOUNT,
        PAYMENT_METHODS.BULK_BANK_ACCOUNT,
        PAYMENT_METHODS.MOBILE_MONEY
      ].includes(
        paymentMethod as
          | typeof PAYMENT_METHODS.BANK_TRANSFER
          | typeof PAYMENT_METHODS.BANK_ACCOUNT
          | typeof PAYMENT_METHODS.BULK_BANK_ACCOUNT
          | typeof PAYMENT_METHODS.MOBILE_MONEY
      )
    ) {
      if (!['api', 'web'].includes(current)) return true;
    } else if ([PAYMENT_METHODS.DISBURSEMENT_WALLET].includes(paymentMethod as typeof PAYMENT_METHODS.DISBURSEMENT_WALLET)) {
      if (!['web'].includes(current)) return true;
    }
    return false;
  };

  const channels = category === CATEGORIES.PAYOUTS ? payoutChannels : payinChannels;
  const channelContent = () => {
    const handleSelection = (value: channelsType) => {
      if (selectedChannels.includes(value)) {
        setSelectedChannels(selectedChannels.filter(item => item !== value));
      } else {
        setSelectedChannels([...selectedChannels, value]);
      }
    };

    return (
      <div className="currency-modal__content">
        <div className="radio_container --channels">
          {Object.entries(channels).map(([key, value]) => {
            return (
              <label key={key} className="channel-item">
                <input
                  checked={selectedChannels.includes(key)}
                  type="checkbox"
                  onChange={() => handleSelection(key as channelsType)}
                  disabled={removeChannel(key as channelsType)}
                  style={{
                    cursor: removeChannel(key as channelsType) ? 'not-allowed' : 'pointer'
                  }}
                />
                {key === 'api' ? capitalizeAllCharacters(value) : capitalize(value)}
              </label>
            );
          })}
        </div>
        <div className="info-wrapper --channel-info">
          <Icon name="infoRounded" fill="#2376F3" height={16} width={16} />
          <p className="enable">
            <span>Important:</span>{' '}
            {`The changes you have made here will affect ${
              merchantId ? 'this merchant.' : `all merchants under the ${selectedMode} configuration.`
            }`}
          </p>
        </div>
        <label className="prod-consent-wrapper">
          <input checked={consent} type="checkbox" onChange={() => setConsent(!consent)} />
          <span>Yes, I understand the implications of this action</span>
        </label>
      </div>
    );
  };
  const editVbaCountContent = () => {
    const handleVbaLimitFormChange = (value: string, mode: TVbaIncreaseFormFields) => {
      if (mode === 'reason') {
        setVbaCountsData({
          ...vbaCountsData,
          reason: value
        });
      }
      if (mode === 'counts') {
        setVbaCountsData({
          ...vbaCountsData,
          num: Number(value)
        });
      }
    };

    return (
      <IncreasedVbaCountForm
        content={content as VbaCountType}
        vbaCountsData={vbaCountsData}
        handleFormChange={handleVbaLimitFormChange}
        consent={consent}
        setConsent={setConsent}
      />
    );
  };
  const limitsContent = () => {
    const handleChange = (item: limitsLabelType, value: string) => {
      const formattedValue = backwardAmountInput(cleanInput(value.replace(/,/g, '')));
      setLimits({
        ...limits,
        [item]: String(formattedValue)
      });
    };
    return (
      <div className="currency-modal__content">
        <div className="radio_container --channels">
          {['min', 'max'].map(item => {
            return (
              <div key={item} className="channel-input">
                <span>{limitsLabel[item as limitsLabelType]}</span>
                <input
                  value={formatWithCommas(formatAmount(String(limits[item as limitsLabelType])))}
                  placeholder={`Enter ${item} value`}
                  type="numeric"
                  onChange={e => handleChange(item as limitsLabelType, cleanInput(e.target.value) as string)}
                />
              </div>
            );
          })}
        </div>
        <div className="info-wrapper --channel-info">
          <Icon name="infoRounded" fill="#2376F3" height={16} width={16} />
          <p className="enable">
            <span>Important:</span>{' '}
            {`The changes you have made here will affect ${
              merchantId ? 'this merchant.' : `all merchants under the ${selectedMode} configuration`
            }`}
          </p>
        </div>
        <label className="prod-consent-wrapper">
          <input checked={consent} type="checkbox" onChange={() => setConsent(!consent)} />
          <span>Yes, I understand the implications of this action</span>
        </label>
      </div>
    );
  };

  const modalDetails = (types: ModalType) => {
    let contents;
    switch (types) {
      case 'all':
        contents = {
          heading:
            title === CONFIG_TITLES.CHANNELS
              ? 'Edit channels product config'
              : `Edit 'Limits' for ${currency} ${capitalizeRemovedash(paymentMethod)}`,
          description: 'Please select an option to continue',
          secondButtonText: 'Continue',
          content: allAccessContent(),
          secondButtonDisable: !selected,
          secondButtonActionIsTerminal: false,
          secondButtonAction: () => {
            if (title === CONFIG_TITLES.TRANSACTION_LIMIT) {
              setModal('transaction_limit');
            } else if (title === CONFIG_TITLES.CHANNELS) {
              setModal('channels');
            } else if (title === CONFIG_TITLES.LIMITS) {
              setModal('limits');
            }
          }
        };
        break;
      case 'transaction_limit':
        contents = {
          heading: `Edit ‘Limits’ for ${currency} ${capitalizeRemovedash(paymentMethod)}`,
          description: 'You can edit the limits for this product category by clicking into the input fields. ',
          secondButtonText: 'Confirm & Edit',
          completedDescription: 'You have made changes to the payment channels under this payment category.',
          content: limitsContent(),
          secondButtonDisable: !(limits.max && limits.min && consent) || limits.max <= limits.min,
          secondButtonAction: async () => {
            return updateProductConfiguration.mutateAsync({
              currency,
              payment_type: productMapping[category],
              payment_method: paymentMethod,
              type: selected || 'single_merchant',
              data: {
                enabled: true,
                transaction_limit: {
                  min: limits.min,
                  max: limits.max
                }
              },
              account_id: merchantId
            });
          }
        };
        break;
      case 'channels':
        contents = {
          heading: 'Edit channels product config',
          description: 'Please select the payment channels products you would like to enable under this payment category',
          secondButtonText: 'Confirm & Edit',
          content: channelContent(),
          completedDescription: 'You have made changes to the payment channels under this payment category.',
          secondButtonDisable: !(selectedChannels.length && consent),
          secondButtonAction: async () => {
            return updateProductConfiguration.mutateAsync({
              currency,
              payment_type: productMapping[category],
              payment_method: paymentMethod,
              type: selected ?? 'single_merchant',
              data: {
                enabled: true,
                channels: selectedChannels
              },
              account_id: merchantId
            });
          }
        };
        break;
      case 'vba_count':
        contents = {
          heading: `Edit VBA Count`,
          description: 'You can edit the number of assigned VBAs for this merchant by clicking into the input fields.',
          secondButtonText: 'Confirm and Update',
          completedDescription: 'You have successfully increased the number of assigned VBAs',
          completedHeading: 'VBA Increased',
          content: editVbaCountContent(),
          secondButtonDisable: !(
            consent &&
            vbaCountsData.num > 0 &&
            vbaCountsData.reason.length > MIN_WORD_COUNT &&
            vbaCountsData.reason.length < VBA_LIMIT_MAX_COUNT
          ),
          secondButtonAction: async () => {
            return updateVbaCount.mutateAsync({
              count: vbaCountsData.num || 0,
              reason: vbaCountsData.reason || '',
              currency
            });
          }
        };
        break;
      case 'limits':
        contents = {
          heading: `Edit Withdrawal Limit  For ${currency} ${capitalizeRemovedash(paymentMethod)}`,
          description: 'You can edit the limits for this product category by clicking into the input fields',
          secondButtonText: 'Confirm & Edit',
          completedDescription: `You have made changes to the Withdrawal limit for ${currency} ${capitalizeRemovedash(paymentMethod)}`,
          completedHeading: 'Success',
          content: (
            <div className="currency-modal__content">
              <WithdrawalLimitContent
                content={withdrawalLimitData as WithdrawalLimitType}
                transactionLimit={
                  (content as WithdrawalLimitType).limit ?? {
                    min: 0,
                    max: 0
                  }
                }
                handleWithdrawalLimitChange={handleWithdrawalLimitChange}
                merchantConfigType={selected}
                selectedWithdrawalType={activeWithdrawalLimitType}
              />
              <label className="prod-consent-wrapper">
                <input checked={consent} type="checkbox" onChange={() => setConsent(!consent)} data-testid="consent-checkbox" />
                <span>Yes, I understand the implications of this action</span>
              </label>
            </div>
          ),
          isScrollable: true,
          secondButtonDisable: !consent || anyValidationError,
          secondButtonAction: async () => {
            if (anyValidationError) return;

            // Debug: Log the data for troubleshooting
            console.log('=== DEBUGGING WITHDRAWAL LIMITS OPTIMIZATION ===');
            console.log('Original data:', JSON.stringify(originalWithdrawalLimitData, null, 2));
            console.log('Current data:', JSON.stringify(withdrawalLimitData, null, 2));
            console.log('Remove web for non-mobile money:', removeWebLimitForNonMobileMoney);

            // Check if we have original data for comparison
            if (!originalWithdrawalLimitData) {
              console.error('ERROR: originalWithdrawalLimitData is undefined! Cannot optimize payload.');
              // Fallback to original behavior if no original data
              const limitsPayload: any = {
                api: (withdrawalLimitData as WithdrawalLimitType).api
              };
              if (!removeWebLimitForNonMobileMoney) {
                limitsPayload.web = (withdrawalLimitData as WithdrawalLimitType).web;
              }
              console.log('Using fallback payload:', JSON.stringify(limitsPayload, null, 2));

              return updateProductConfiguration.mutateAsync({
                currency,
                payment_type: productMapping[category],
                payment_method: paymentMethod,
                type: selected ?? 'single_merchant',
                data: {
                  enabled: true,
                  limits: limitsPayload
                },
                account_id: merchantId
              });
            }

            // Create optimized payload with only changed sections
            const optimizedLimits = createOptimizedWithdrawalLimitsPayload(
              originalWithdrawalLimitData,
              withdrawalLimitData as WithdrawalLimitType,
              { web: removeWebLimitForNonMobileMoney }
            );

            console.log('Optimized limits result:', JSON.stringify(optimizedLimits, null, 2));

            // Use the optimized payload directly (it only contains changed sections)
            const limitsPayload = optimizedLimits;

            console.log('Final payload:', JSON.stringify(limitsPayload, null, 2));
            console.log('=== END DEBUG ===');

            return updateProductConfiguration.mutateAsync({
              currency,
              payment_type: productMapping[category],
              payment_method: paymentMethod,
              type: selected ?? 'single_merchant',
              data: {
                enabled: true,
                limits: limitsPayload
              },
              account_id: merchantId
            });
          }
        };
        break;
      default:
        contents = {};
    }

    return {
      close: () => {
        setModal(null);
        setSelected(undefined);
        setSelectedChannels([]);
        setConsent(false);
        setLimits({
          min: 0,
          max: 0
        });
        setVbaCountsData({
          num: 0,
          reason: ''
        });
        resetWithdrawalLimitData();
      },
      secondaryCompletedModal: true,
      ...contents
    };
  };

  const editDetailsContent = (value: updateProductConfigDataTypeWithoutEnabled) => {
    switch (value) {
      case 'channels':
        return Object.entries(channels).map(([key, values]) => {
          if (removeChannel(key as channelsType)) return null;
          return (
            <div key={key} className="menu">
              <p>{`Payment via ${values}`}:</p>{' '}
              <span>{[...new Set(content as channelsType[])].includes(key as channelsType) ? 'Enabled' : 'Disabled'}</span>
            </div>
          );
        });
      case 'transaction_limit':
        return Object.entries(content as TransactionLimitType).map(([key, values]) => (
          <div key={key} className="menu">
            <p>{limitsLabel[key as limitsLabelType]}:</p> <span>{formatAmount(Number(values))}</span>
          </div>
        ));
      case 'vba_count':
        return <VbaLimitDetails content={content as VbaCountType} />;
      case 'limits':
        return <WithdrawalLimitDetails content={content as WithdrawalLimitType} selectedWithdrawalType={activeWithdrawalLimitType} />;
      default:
        return null;
    }
  };
  return (
    <>
      <div className="editable-card">
        <Typography variant="h4" className="editable-card__header">
          {getHeaderLabel(title)}
        </Typography>
        {category === CATEGORIES.PAYOUTS &&
          [PAYMENT_METHODS.BANK_ACCOUNT, PAYMENT_METHODS.MOBILE_MONEY].includes(
            paymentMethod as typeof PAYMENT_METHODS.BANK_ACCOUNT | typeof PAYMENT_METHODS.MOBILE_MONEY
          ) &&
          title === CONFIG_TITLES.LIMITS && (
            <div className="editable-card__tab">
              <TabSwitch
                options={getTabOptions(content)}
                setTab={value => handleTabChange(value as { value: string })}
                activeTab={getActiveTabName()}
                showDropDown={false}
                identifier="label"
              />
            </div>
          )}
        {conditionToDisplayBanner(title, content) ? <ProductConfigBanner type="warning" mode={title} /> : null}
        <div className="editable-card__body">
          <div className="first-section">
            <p className="card-title">{description[title as keyof typeof description]}</p>
            {isAllowed(userAccess, ['transaction_config_details.update']) && disableEdit && (
              <span
                role="button"
                className="card-action"
                onClick={() => setModal((!merchantId ? 'all' : title) as ModalType)}
                onKeyDown={() => setModal((!merchantId ? 'all' : title) as ModalType)}
                tabIndex={0}
              >
                Edit
              </span>
            )}
          </div>
          <div className="second-section">{editDetailsContent(title as updateProductConfigDataTypeWithoutEnabled)}</div>
        </div>
      </div>

      <Modal
        visible={!!modal}
        size="md"
        completedModalSize="base"
        equalFooterBtn
        showCompleteActionText
        {...(modalDetails(modal) as IModalProps)}
      />
    </>
  );
};

export default EditDetailsCard;
