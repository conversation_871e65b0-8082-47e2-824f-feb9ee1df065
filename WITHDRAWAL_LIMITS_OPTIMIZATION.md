# Withdrawal Limits Optimization

## Overview

This document describes the implementation of optimized withdrawal limit submissions that only send changed fields to the backend, preventing unnecessary errors and improving API efficiency.

## Problem

Previously, the system would always submit all withdrawal limit fields (`web`, `api`, `global`) regardless of whether they were actually modified by the user. This caused:

1. **Backend errors** when unchanged fields were submitted
2. **Unnecessary API calls** with redundant data
3. **Poor user experience** due to failed submissions

## Solution

### 1. Change Tracking Utility

Created `useWithdrawalLimitChanges.ts` with two main exports:

#### `useWithdrawalLimitChanges` Hook
- Tracks changes to withdrawal limit data in real-time
- Provides methods to detect which sections were modified
- Generates optimized payloads with only changed sections

#### `createOptimizedWithdrawalLimitsPayload` Function
- Utility function for comparing original vs current data
- Returns only the sections that have been modified
- Supports exclude conditions for special cases

### 2. Implementation in Components

#### EditDetailsCard.tsx
```typescript
// Track original data
const [originalWithdrawalLimitData, setOriginalWithdrawalLimitData] = useState<WithdrawalLimitType | undefined>();

// Store original data when first loaded
useEffect(() => {
  if (withdrawalLimitData && !originalWithdrawalLimitData) {
    setOriginalWithdrawalLimitData(JSON.parse(JSON.stringify(withdrawalLimitData)));
  }
}, [withdrawalLimitData, originalWithdrawalLimitData]);

// Optimized submission
const optimizedLimits = createOptimizedWithdrawalLimitsPayload(
  originalWithdrawalLimitData,
  withdrawalLimitData as WithdrawalLimitType,
  { web: removeWebLimitForNonMobileMoney } // Exclude web for non-mobile money
);
```

#### GlobalLimitModal.tsx
Similar implementation but without the mobile money exclusion logic.

## How It Works

### 1. Data Tracking
- When withdrawal limit data first loads, we store a deep copy as the "original" state
- This original state serves as the baseline for change detection

### 2. Change Detection
- Uses `lodash.isEqual` for deep comparison between original and current data
- Detects changes at the section level (`web`, `api`, `global`)
- Handles nested object changes correctly

### 3. Payload Optimization
- Only includes sections that have actually changed
- Respects exclude conditions (e.g., removing `web` for non-mobile money currencies)
- Returns an empty object if no changes were made

### 4. Submission Logic
```typescript
// Build the final payload, ensuring we only send what was changed
const limitsPayload: any = {};

// Only include sections that were actually changed
if (optimizedLimits.web && !removeWebLimitForNonMobileMoney) {
  limitsPayload.web = optimizedLimits.web;
}
if (optimizedLimits.api) {
  limitsPayload.api = optimizedLimits.api;
}
if (optimizedLimits.global) {
  limitsPayload.global = optimizedLimits.global;
}
```

## Benefits

### 1. Reduced Backend Errors
- Only modified sections are submitted
- Prevents validation errors on unchanged fields
- Improves success rate of API calls

### 2. Better Performance
- Smaller payload sizes
- Reduced network traffic
- Faster API responses

### 3. Improved User Experience
- Fewer failed submissions
- More predictable behavior
- Better error handling

### 4. Maintainable Code
- Centralized change detection logic
- Reusable across components
- Clear separation of concerns

## Usage Examples

### Example 1: Only Web Limits Changed
```typescript
// Original data
{
  web: { daily: { settlement_account: 100, non_settlement_account: 50 } },
  api: { per_transaction: { min: 20, max: 2000 } },
  global: { daily: 5000 }
}

// User changes only web limits
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } }, // Changed
  api: { per_transaction: { min: 20, max: 2000 } }, // Unchanged
  global: { daily: 5000 } // Unchanged
}

// Optimized payload (only web is sent)
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } }
}
```

### Example 2: Multiple Sections Changed
```typescript
// User changes both web and global limits
// Optimized payload includes both sections
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } },
  global: { daily: 6000 }
}
```

### Example 3: No Changes Made
```typescript
// If no changes were made, payload is empty
{}
```

## Testing

The implementation includes comprehensive tests covering:
- No changes scenario
- Single section changes
- Multiple section changes
- Exclude conditions
- Deep nested changes
- New sections added

## Future Enhancements

1. **Real-time Change Indicators**: Visual feedback showing which sections have been modified
2. **Undo/Redo Functionality**: Allow users to revert changes before submission
3. **Change Summary**: Show users exactly what will be submitted before confirmation
4. **Granular Field Tracking**: Track changes at individual field level rather than section level

## Migration Notes

- Existing functionality remains unchanged
- No breaking changes to existing APIs
- Backward compatible with current data structures
- Can be gradually rolled out to other similar components
